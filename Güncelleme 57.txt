custom-dashboard-page.css
içine sidebar stileri bölümünde en alta bu kodu ekledim.
bu sayade artık aktif olan sekmenin üstüne gelindiğinde hover gözükmeyecek

/* Tüm aktif menü öğeleri için stil */
.tutor-dashboard-menu-item-link.is-active,
.tutor-dashboard-menu-item-link.active,
.tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 20%, transparent) !important;
  color: var(--tutor-color-primary) !important;
  font-weight: 600 !important;
  border-left: 3px solid var(--tutor-color-primary) !important;
}


/* Tüm aktif menü öğelerinin ikonları için stil */
 .tutor-dashboard-menu-item-link.is-active i,
 .tutor-dashboard-menu-item-link.active i,
 .tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link i,
 .tutor-dashboard-menu-item-link.is-active .tutor-dashboard-menu-item-icon,
 .tutor-dashboard-menu-item-link.active .tutor-dashboard-menu-item-icon,
 .tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-icon {
   color: var(--tutor-color-primary) !important;
}
 


